import matplotlib.pyplot as plt
import numpy as np

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def test_chinese_font():
    """测试中文字体显示"""
    # 创建测试数据
    x = np.linspace(0, 10, 100)
    y = np.sin(x)
    
    # 创建图形
    plt.figure(figsize=(10, 6))
    plt.plot(x, y, 'b-', linewidth=2, label='正弦函数')
    
    # 设置中文标题和标签
    plt.title('中文字体测试 - 正弦函数图像', fontsize=16)
    plt.xlabel('横坐标 (x)', fontsize=12)
    plt.ylabel('纵坐标 (y)', fontsize=12)
    plt.legend(fontsize=12)
    
    # 添加网格
    plt.grid(True, alpha=0.3)
    
    # 添加文本注释
    plt.text(5, 0.5, '这是中文注释文本', fontsize=14, 
             bbox=dict(boxstyle="round,pad=0.3", facecolor="yellow", alpha=0.7))
    
    # 保存图片
    plt.savefig('chinese_font_test.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("中文字体测试完成！")
    print("如果图像中的中文显示正常，说明字体配置成功。")

if __name__ == "__main__":
    test_chinese_font()
