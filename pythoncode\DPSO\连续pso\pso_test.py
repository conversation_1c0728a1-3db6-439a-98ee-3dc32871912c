import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
from scipy import stats

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']  # 用来正常显示中文标签
plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号

class PSO:
    def __init__(self, n_particles=30, n_dimensions=10, max_iter=100):
        self.n_particles = n_particles
        self.n_dimensions = n_dimensions
        self.max_iter = max_iter
        
        # PSO参数
        self.w = 0.7  # 惯性权重
        self.c1 = 1.5  # 个体学习因子
        self.c2 = 1.5  # 社会学习因子
        
        # 初始化粒子位置和速度
        self.positions = np.random.uniform(-10, 10, (n_particles, n_dimensions))
        self.velocities = np.random.uniform(-1, 1, (n_particles, n_dimensions))
        
        # 个体最优和全局最优
        self.pbest_positions = self.positions.copy()
        self.pbest_fitness = np.full(n_particles, float('inf'))
        self.gbest_position = None
        self.gbest_fitness = float('inf')
        
        self.fitness_history = []

        # 统计分析历史记录
        self.kurtosis_history = []  # 峰度历史
        self.skewness_history = []  # 偏度历史
        self.population_stats = []  # 种群统计信息

    def sphere_function(self, x):
        """球函数：f(x) = sum(xi^2)"""
        return np.sum(x**2)
    
    def rastrigin_function(self, x):
        """Rastrigin函数：多峰函数"""
        A = 10
        n = len(x)
        return A * n + np.sum(x**2 - A * np.cos(2 * np.pi * x))
    
    def rosenbrock_function(self, x):
        """Rosenbrock函数：香蕉函数"""
        return np.sum(100 * (x[1:] - x[:-1]**2)**2 + (1 - x[:-1])**2)

    def calculate_population_statistics(self, fitness_values):
        """计算种群的统计指标"""
        # 计算峰度和偏度
        kurtosis = stats.kurtosis(fitness_values, fisher=True)  # Fisher=True表示超额峰度
        skewness = stats.skew(fitness_values)

        # 其他统计指标
        mean_fitness = np.mean(fitness_values)
        std_fitness = np.std(fitness_values)
        min_fitness = np.min(fitness_values)
        max_fitness = np.max(fitness_values)

        stats_dict = {
            'kurtosis': kurtosis,
            'skewness': skewness,
            'mean': mean_fitness,
            'std': std_fitness,
            'min': min_fitness,
            'max': max_fitness
        }

        return stats_dict

    def optimize(self, objective_func):
        for iteration in range(self.max_iter):
            # 评估适应度
            current_fitness = []
            for i in range(self.n_particles):
                fitness = objective_func(self.positions[i])
                current_fitness.append(fitness)

                # 更新个体最优
                if fitness < self.pbest_fitness[i]:
                    self.pbest_fitness[i] = fitness
                    self.pbest_positions[i] = self.positions[i].copy()

                # 更新全局最优
                if fitness < self.gbest_fitness:
                    self.gbest_fitness = fitness
                    self.gbest_position = self.positions[i].copy()

            # 计算当前代的统计指标
            stats_dict = self.calculate_population_statistics(current_fitness)
            self.population_stats.append(stats_dict)
            self.kurtosis_history.append(stats_dict['kurtosis'])
            self.skewness_history.append(stats_dict['skewness'])

            # 更新速度和位置
            for i in range(self.n_particles):
                r1, r2 = np.random.random(2)

                self.velocities[i] = (self.w * self.velocities[i] +
                                    self.c1 * r1 * (self.pbest_positions[i] - self.positions[i]) +
                                    self.c2 * r2 * (self.gbest_position - self.positions[i]))

                self.positions[i] += self.velocities[i]

            self.fitness_history.append(self.gbest_fitness)

            if iteration % 20 == 0:
                print(f"迭代 {iteration}: 最优适应度 = {self.gbest_fitness:.6f}, "
                      f"峰度 = {stats_dict['kurtosis']:.3f}, 偏度 = {stats_dict['skewness']:.3f}")

        return self.gbest_position, self.gbest_fitness

def plot_statistics_analysis(pso_instance, func_name):
    """绘制统计分析图表"""
    iterations = range(len(pso_instance.kurtosis_history))

    # 创建图形
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))

    # 1. 峰度变化图
    ax1.plot(iterations, pso_instance.kurtosis_history, 'b-', linewidth=2, label='峰度')
    ax1.axhline(y=3, color='r', linestyle='--', alpha=0.7, label='峰度=3基准线')

    # 标记峰度大于3的点
    high_kurtosis_indices = [i for i, k in enumerate(pso_instance.kurtosis_history) if k > 3]
    if high_kurtosis_indices:
        high_kurtosis_values = [pso_instance.kurtosis_history[i] for i in high_kurtosis_indices]
        ax1.scatter(high_kurtosis_indices, high_kurtosis_values,
                   color='red', s=50, alpha=0.8, label=f'峰度>3 ({len(high_kurtosis_indices)}个点)')

    ax1.set_title(f'{func_name} - 种群适应度峰度变化')
    ax1.set_xlabel('迭代次数')
    ax1.set_ylabel('峰度值')
    ax1.legend()
    ax1.grid(True, alpha=0.3)

    # 2. 偏度变化图
    ax2.plot(iterations, pso_instance.skewness_history, 'g-', linewidth=2, label='偏度')
    ax2.axhline(y=0, color='r', linestyle='--', alpha=0.7, label='偏度=0基准线')

    # 标记偏度大于0的点
    positive_skew_indices = [i for i, s in enumerate(pso_instance.skewness_history) if s > 0]
    if positive_skew_indices:
        positive_skew_values = [pso_instance.skewness_history[i] for i in positive_skew_indices]
        ax2.scatter(positive_skew_indices, positive_skew_values,
                   color='orange', s=50, alpha=0.8, label=f'偏度>0 ({len(positive_skew_indices)}个点)')

    ax2.set_title(f'{func_name} - 种群适应度偏度变化')
    ax2.set_xlabel('迭代次数')
    ax2.set_ylabel('偏度值')
    ax2.legend()
    ax2.grid(True, alpha=0.3)

    # 3. 峰度vs偏度散点图
    ax3.scatter(pso_instance.skewness_history, pso_instance.kurtosis_history,
               alpha=0.6, c=iterations, cmap='viridis', s=30)

    # 标记特殊区域
    special_points_x = []
    special_points_y = []
    for i, (s, k) in enumerate(zip(pso_instance.skewness_history, pso_instance.kurtosis_history)):
        if k > 3 and s > 0:
            special_points_x.append(s)
            special_points_y.append(k)

    if special_points_x:
        ax3.scatter(special_points_x, special_points_y,
                   color='red', s=80, alpha=0.8, marker='x', linewidth=3,
                   label=f'峰度>3且偏度>0 ({len(special_points_x)}个点)')

    ax3.axhline(y=3, color='r', linestyle='--', alpha=0.5)
    ax3.axvline(x=0, color='r', linestyle='--', alpha=0.5)
    ax3.set_title(f'{func_name} - 峰度vs偏度分布')
    ax3.set_xlabel('偏度')
    ax3.set_ylabel('峰度')
    ax3.legend()
    ax3.grid(True, alpha=0.3)

    # 添加颜色条
    cbar = plt.colorbar(ax3.collections[0], ax=ax3)
    cbar.set_label('迭代次数')

    # 4. 适应度收敛曲线
    ax4.plot(iterations, pso_instance.fitness_history, 'purple', linewidth=2, label='最优适应度')
    ax4.set_title(f'{func_name} - 适应度收敛曲线')
    ax4.set_xlabel('迭代次数')
    ax4.set_ylabel('适应度值')
    ax4.set_yscale('log')
    ax4.legend()
    ax4.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig(f'{func_name}_statistics_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()

    # 打印统计摘要
    print(f"\n=== {func_name} 统计分析摘要 ===")
    print(f"峰度大于3的迭代次数: {len(high_kurtosis_indices)} / {len(iterations)}")
    print(f"偏度大于0的迭代次数: {len(positive_skew_indices)} / {len(iterations)}")
    print(f"同时满足峰度>3且偏度>0的迭代次数: {len(special_points_x)} / {len(iterations)}")
    print(f"平均峰度: {np.mean(pso_instance.kurtosis_history):.3f}")
    print(f"平均偏度: {np.mean(pso_instance.skewness_history):.3f}")
    print(f"峰度范围: [{np.min(pso_instance.kurtosis_history):.3f}, {np.max(pso_instance.kurtosis_history):.3f}]")
    print(f"偏度范围: [{np.min(pso_instance.skewness_history):.3f}, {np.max(pso_instance.skewness_history):.3f}]")

def plot_function_surface(func, func_name, ax, x_range=(-5, 5), y_range=(-5, 5)):
    """绘制函数的3D表面图"""
    x = np.linspace(x_range[0], x_range[1], 100)
    y = np.linspace(y_range[0], y_range[1], 100)
    X, Y = np.meshgrid(x, y)

    # 计算函数值
    Z = np.zeros_like(X)
    for i in range(X.shape[0]):
        for j in range(X.shape[1]):
            point = np.array([X[i, j], Y[i, j]])
            Z[i, j] = func(point)

    # 绘制3D表面
    surf = ax.plot_surface(X, Y, Z, cmap='viridis', alpha=0.7)
    ax.set_title(f'{func_name} 函数表面')
    ax.set_xlabel('x1')
    ax.set_ylabel('x2')
    ax.set_zlabel('f(x1, x2)')

    return surf

def plot_functions_and_convergence(results, dimensions):
    """绘制函数表面和收敛曲线"""

    functions = {
        'Sphere': PSO().sphere_function,
        'Rastrigin': PSO().rastrigin_function,
        'Rosenbrock': PSO().rosenbrock_function
    }

    # 为每个函数创建单独的图形
    for func_name, func in functions.items():
        fig = plt.figure(figsize=(15, 6))

        # 左侧：函数3D表面图
        ax1 = fig.add_subplot(121, projection='3d')

        # 根据函数类型调整显示范围
        if func_name == 'Sphere':
            x_range = y_range = (-3, 3)
        elif func_name == 'Rastrigin':
            x_range = y_range = (-5, 5)
        else:  # Rosenbrock
            x_range = y_range = (-2, 2)

        plot_function_surface(func, func_name, ax1, x_range, y_range)

        # 右侧：收敛曲线
        ax2 = fig.add_subplot(122)

        for dim in dimensions:
            if func_name in results and dim in results[func_name]:
                history = results[func_name][dim]['history']
                ax2.plot(history, label=f'{dim}维', linewidth=2)

        ax2.set_title(f'{func_name} 函数收敛曲线')
        ax2.set_xlabel('迭代次数')
        ax2.set_ylabel('适应度值')
        ax2.set_yscale('log')
        ax2.legend()
        ax2.grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig(f'{func_name}_function_and_convergence.png', dpi=300, bbox_inches='tight')
        plt.show()

    # 创建综合收敛曲线对比图
    plt.figure(figsize=(15, 5))

    for i, (func_name, func_results) in enumerate(results.items()):
        plt.subplot(1, 3, i+1)
        for dim in dimensions:
            if dim in func_results:
                history = func_results[dim]['history']
                plt.plot(history, label=f'{dim}维', linewidth=2)

        plt.title(f'{func_name} 函数收敛曲线')
        plt.xlabel('迭代次数')
        plt.ylabel('适应度值')
        plt.yscale('log')
        plt.legend()
        plt.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig('all_functions_convergence.png', dpi=300, bbox_inches='tight')
    plt.show()

def test_high_dimensional_problems():
    """测试不同维度的连续优化问题"""
    dimensions = [10, 30, 50, 100]
    functions = {
        'Sphere': PSO().sphere_function,
        'Rastrigin': PSO().rastrigin_function,
        'Rosenbrock': PSO().rosenbrock_function
    }
    
    results = {}
    
    for func_name, func in functions.items():
        print(f"\n=== 测试 {func_name} 函数 ===")
        results[func_name] = {}
        
        for dim in dimensions:
            print(f"\n维度: {dim}")
            pso = PSO(n_particles=50, n_dimensions=dim, max_iter=200)
            best_pos, best_fitness = pso.optimize(func)
            
            results[func_name][dim] = {
                'best_fitness': best_fitness,
                'best_position': best_pos,
                'history': pso.fitness_history,
                'pso_instance': pso  # 保存PSO实例以便进行统计分析
            }
            
            print(f"最优解: {best_fitness:.6f}")
    
    # 绘制每个函数的3D可视化和收敛曲线
    plot_functions_and_convergence(results, dimensions)
    
    return results

def test_statistics_analysis():
    """专门测试统计分析功能"""
    print("\n=== 开始统计分析测试 ===")

    functions = {
        'Sphere': PSO().sphere_function,
        'Rastrigin': PSO().rastrigin_function,
        'Rosenbrock': PSO().rosenbrock_function
    }

    # 选择一个中等维度进行详细分析
    test_dimension = 30

    for func_name, func in functions.items():
        print(f"\n--- 分析 {func_name} 函数 (维度: {test_dimension}) ---")

        # 创建PSO实例并优化
        pso = PSO(n_particles=50, n_dimensions=test_dimension, max_iter=200)
        best_pos, best_fitness = pso.optimize(func)

        print(f"最优解: {best_fitness:.6f}")

        # 进行统计分析并绘图
        plot_statistics_analysis(pso, func_name)

    print("\n统计分析测试完成！")

if __name__ == "__main__":
    print("PSO优化算法测试程序")
    print("1. 高维连续优化测试")
    print("2. 统计分析测试")

    choice = input("请选择测试类型 (1/2): ").strip()

    if choice == "1":
        print("开始PSO高维连续优化测试...")
        results = test_high_dimensional_problems()

        # 输出总结
        print("\n=== 测试结果总结 ===")
        for func_name, func_results in results.items():
            print(f"\n{func_name} 函数:")
            for dim, result in func_results.items():
                print(f"  {dim}维: {result['best_fitness']:.6f}")

    elif choice == "2":
        test_statistics_analysis()

    else:
        print("无效选择，运行统计分析测试...")
        test_statistics_analysis()