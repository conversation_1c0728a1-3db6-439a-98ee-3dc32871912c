import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']  # 用来正常显示中文标签
plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号

class PSO:
    def __init__(self, n_particles=30, n_dimensions=10, max_iter=100):
        self.n_particles = n_particles
        self.n_dimensions = n_dimensions
        self.max_iter = max_iter
        
        # PSO参数
        self.w = 0.7  # 惯性权重
        self.c1 = 1.5  # 个体学习因子
        self.c2 = 1.5  # 社会学习因子
        
        # 初始化粒子位置和速度
        self.positions = np.random.uniform(-10, 10, (n_particles, n_dimensions))
        self.velocities = np.random.uniform(-1, 1, (n_particles, n_dimensions))
        
        # 个体最优和全局最优
        self.pbest_positions = self.positions.copy()
        self.pbest_fitness = np.full(n_particles, float('inf'))
        self.gbest_position = None
        self.gbest_fitness = float('inf')
        
        self.fitness_history = []

    def sphere_function(self, x):
        """球函数：f(x) = sum(xi^2)"""
        return np.sum(x**2)
    
    def rastrigin_function(self, x):
        """Rastrigin函数：多峰函数"""
        A = 10
        n = len(x)
        return A * n + np.sum(x**2 - A * np.cos(2 * np.pi * x))
    
    def rosenbrock_function(self, x):
        """Rosenbrock函数：香蕉函数"""
        return np.sum(100 * (x[1:] - x[:-1]**2)**2 + (1 - x[:-1])**2)

    def optimize(self, objective_func):
        for iteration in range(self.max_iter):
            # 评估适应度
            for i in range(self.n_particles):
                fitness = objective_func(self.positions[i])
                
                # 更新个体最优
                if fitness < self.pbest_fitness[i]:
                    self.pbest_fitness[i] = fitness
                    self.pbest_positions[i] = self.positions[i].copy()
                
                # 更新全局最优
                if fitness < self.gbest_fitness:
                    self.gbest_fitness = fitness
                    self.gbest_position = self.positions[i].copy()
            
            # 更新速度和位置
            for i in range(self.n_particles):
                r1, r2 = np.random.random(2)
                
                self.velocities[i] = (self.w * self.velocities[i] + 
                                    self.c1 * r1 * (self.pbest_positions[i] - self.positions[i]) +
                                    self.c2 * r2 * (self.gbest_position - self.positions[i]))
                
                self.positions[i] += self.velocities[i]
            
            self.fitness_history.append(self.gbest_fitness)
            
            if iteration % 20 == 0:
                print(f"迭代 {iteration}: 最优适应度 = {self.gbest_fitness:.6f}")
        
        return self.gbest_position, self.gbest_fitness

def plot_function_surface(func, func_name, ax, x_range=(-5, 5), y_range=(-5, 5)):
    """绘制函数的3D表面图"""
    x = np.linspace(x_range[0], x_range[1], 100)
    y = np.linspace(y_range[0], y_range[1], 100)
    X, Y = np.meshgrid(x, y)

    # 计算函数值
    Z = np.zeros_like(X)
    for i in range(X.shape[0]):
        for j in range(X.shape[1]):
            point = np.array([X[i, j], Y[i, j]])
            Z[i, j] = func(point)

    # 绘制3D表面
    surf = ax.plot_surface(X, Y, Z, cmap='viridis', alpha=0.7)
    ax.set_title(f'{func_name} 函数表面')
    ax.set_xlabel('x1')
    ax.set_ylabel('x2')
    ax.set_zlabel('f(x1, x2)')

    return surf

def plot_functions_and_convergence(results, dimensions):
    """绘制函数表面和收敛曲线"""

    functions = {
        'Sphere': PSO().sphere_function,
        'Rastrigin': PSO().rastrigin_function,
        'Rosenbrock': PSO().rosenbrock_function
    }

    # 为每个函数创建单独的图形
    for func_name, func in functions.items():
        fig = plt.figure(figsize=(15, 6))

        # 左侧：函数3D表面图
        ax1 = fig.add_subplot(121, projection='3d')

        # 根据函数类型调整显示范围
        if func_name == 'Sphere':
            x_range = y_range = (-3, 3)
        elif func_name == 'Rastrigin':
            x_range = y_range = (-5, 5)
        else:  # Rosenbrock
            x_range = y_range = (-2, 2)

        plot_function_surface(func, func_name, ax1, x_range, y_range)

        # 右侧：收敛曲线
        ax2 = fig.add_subplot(122)

        for dim in dimensions:
            if func_name in results and dim in results[func_name]:
                history = results[func_name][dim]['history']
                ax2.plot(history, label=f'{dim}维', linewidth=2)

        ax2.set_title(f'{func_name} 函数收敛曲线')
        ax2.set_xlabel('迭代次数')
        ax2.set_ylabel('适应度值')
        ax2.set_yscale('log')
        ax2.legend()
        ax2.grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig(f'{func_name}_function_and_convergence.png', dpi=300, bbox_inches='tight')
        plt.show()

    # 创建综合收敛曲线对比图
    plt.figure(figsize=(15, 5))

    for i, (func_name, func_results) in enumerate(results.items()):
        plt.subplot(1, 3, i+1)
        for dim in dimensions:
            if dim in func_results:
                history = func_results[dim]['history']
                plt.plot(history, label=f'{dim}维', linewidth=2)

        plt.title(f'{func_name} 函数收敛曲线')
        plt.xlabel('迭代次数')
        plt.ylabel('适应度值')
        plt.yscale('log')
        plt.legend()
        plt.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig('all_functions_convergence.png', dpi=300, bbox_inches='tight')
    plt.show()

def test_high_dimensional_problems():
    """测试不同维度的连续优化问题"""
    dimensions = [10, 30, 50, 100]
    functions = {
        'Sphere': PSO().sphere_function,
        'Rastrigin': PSO().rastrigin_function,
        'Rosenbrock': PSO().rosenbrock_function
    }
    
    results = {}
    
    for func_name, func in functions.items():
        print(f"\n=== 测试 {func_name} 函数 ===")
        results[func_name] = {}
        
        for dim in dimensions:
            print(f"\n维度: {dim}")
            pso = PSO(n_particles=50, n_dimensions=dim, max_iter=200)
            best_pos, best_fitness = pso.optimize(func)
            
            results[func_name][dim] = {
                'best_fitness': best_fitness,
                'best_position': best_pos,
                'history': pso.fitness_history
            }
            
            print(f"最优解: {best_fitness:.6f}")
    
    # 绘制每个函数的3D可视化和收敛曲线
    plot_functions_and_convergence(results, dimensions)
    
    return results

if __name__ == "__main__":
    print("开始PSO高维连续优化测试...")
    results = test_high_dimensional_problems()
    
    # 输出总结
    print("\n=== 测试结果总结 ===")
    for func_name, func_results in results.items():
        print(f"\n{func_name} 函数:")
        for dim, result in func_results.items():
            print(f"  {dim}维: {result['best_fitness']:.6f}")