import numpy as np
import matplotlib.pyplot as plt
from scipy import stats

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class SimplePSO:
    def __init__(self, n_particles=30, max_iter=100):
        self.n_particles = n_particles
        self.max_iter = max_iter
        
        # 统计分析历史记录
        self.kurtosis_history = []
        self.skewness_history = []
        self.fitness_history = []
        
    def sphere_function(self, x):
        """球函数：f(x) = sum(xi^2)"""
        return np.sum(x**2)
    
    def calculate_population_statistics(self, fitness_values):
        """计算种群的统计指标"""
        kurtosis = stats.kurtosis(fitness_values, fisher=True)
        skewness = stats.skew(fitness_values)
        return {'kurtosis': kurtosis, 'skewness': skewness}
    
    def optimize(self):
        """简化的PSO优化过程"""
        # 初始化粒子
        positions = np.random.uniform(-10, 10, (self.n_particles, 2))
        velocities = np.random.uniform(-1, 1, (self.n_particles, 2))
        
        best_fitness = float('inf')
        
        for iteration in range(self.max_iter):
            # 计算适应度
            fitness_values = []
            for i in range(self.n_particles):
                fitness = self.sphere_function(positions[i])
                fitness_values.append(fitness)
                
                if fitness < best_fitness:
                    best_fitness = fitness
            
            # 计算统计指标
            stats_dict = self.calculate_population_statistics(fitness_values)
            self.kurtosis_history.append(stats_dict['kurtosis'])
            self.skewness_history.append(stats_dict['skewness'])
            self.fitness_history.append(best_fitness)
            
            # 简单的位置更新
            positions += velocities * 0.1
            velocities *= 0.9  # 减速
            
            if iteration % 20 == 0:
                print(f"迭代 {iteration}: 峰度={stats_dict['kurtosis']:.3f}, "
                      f"偏度={stats_dict['skewness']:.3f}, 最优适应度={best_fitness:.6f}")

def plot_simple_statistics():
    """绘制简化的统计分析图"""
    pso = SimplePSO(n_particles=50, max_iter=100)
    pso.optimize()
    
    iterations = range(len(pso.kurtosis_history))
    
    # 创建图形
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(12, 8))
    
    # 1. 峰度变化图
    ax1.plot(iterations, pso.kurtosis_history, 'b-', linewidth=2, label='峰度')
    ax1.axhline(y=3, color='r', linestyle='--', alpha=0.7, label='峰度=3基准线')
    
    # 标记峰度大于3的点
    high_kurtosis_indices = [i for i, k in enumerate(pso.kurtosis_history) if k > 3]
    if high_kurtosis_indices:
        high_kurtosis_values = [pso.kurtosis_history[i] for i in high_kurtosis_indices]
        ax1.scatter(high_kurtosis_indices, high_kurtosis_values, 
                   color='red', s=50, alpha=0.8, label=f'峰度>3 ({len(high_kurtosis_indices)}个点)')
    
    ax1.set_title('种群适应度峰度变化')
    ax1.set_xlabel('迭代次数')
    ax1.set_ylabel('峰度值')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 2. 偏度变化图
    ax2.plot(iterations, pso.skewness_history, 'g-', linewidth=2, label='偏度')
    ax2.axhline(y=0, color='r', linestyle='--', alpha=0.7, label='偏度=0基准线')
    
    # 标记偏度大于0的点
    positive_skew_indices = [i for i, s in enumerate(pso.skewness_history) if s > 0]
    if positive_skew_indices:
        positive_skew_values = [pso.skewness_history[i] for i in positive_skew_indices]
        ax2.scatter(positive_skew_indices, positive_skew_values, 
                   color='orange', s=50, alpha=0.8, label=f'偏度>0 ({len(positive_skew_indices)}个点)')
    
    ax2.set_title('种群适应度偏度变化')
    ax2.set_xlabel('迭代次数')
    ax2.set_ylabel('偏度值')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    # 3. 峰度vs偏度散点图
    scatter = ax3.scatter(pso.skewness_history, pso.kurtosis_history, 
                         alpha=0.6, c=iterations, cmap='viridis', s=30)
    
    # 标记特殊区域
    special_points_x = []
    special_points_y = []
    for s, k in zip(pso.skewness_history, pso.kurtosis_history):
        if k > 3 and s > 0:
            special_points_x.append(s)
            special_points_y.append(k)
    
    if special_points_x:
        ax3.scatter(special_points_x, special_points_y, 
                   color='red', s=80, alpha=0.8, marker='x', linewidth=3,
                   label=f'峰度>3且偏度>0 ({len(special_points_x)}个点)')
    
    ax3.axhline(y=3, color='r', linestyle='--', alpha=0.5)
    ax3.axvline(x=0, color='r', linestyle='--', alpha=0.5)
    ax3.set_title('峰度vs偏度分布')
    ax3.set_xlabel('偏度')
    ax3.set_ylabel('峰度')
    if special_points_x:
        ax3.legend()
    ax3.grid(True, alpha=0.3)
    
    # 添加颜色条
    plt.colorbar(scatter, ax=ax3, label='迭代次数')
    
    # 4. 适应度收敛曲线
    ax4.plot(iterations, pso.fitness_history, 'purple', linewidth=2, label='最优适应度')
    ax4.set_title('适应度收敛曲线')
    ax4.set_xlabel('迭代次数')
    ax4.set_ylabel('适应度值')
    ax4.set_yscale('log')
    ax4.legend()
    ax4.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('statistics_analysis_demo.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    # 打印统计摘要
    print(f"\n=== 统计分析摘要 ===")
    print(f"峰度大于3的迭代次数: {len(high_kurtosis_indices)} / {len(iterations)}")
    print(f"偏度大于0的迭代次数: {len(positive_skew_indices)} / {len(iterations)}")
    print(f"同时满足峰度>3且偏度>0的迭代次数: {len(special_points_x)} / {len(iterations)}")
    print(f"平均峰度: {np.mean(pso.kurtosis_history):.3f}")
    print(f"平均偏度: {np.mean(pso.skewness_history):.3f}")

if __name__ == "__main__":
    print("开始PSO统计分析演示...")
    plot_simple_statistics()
    print("统计分析演示完成！")
